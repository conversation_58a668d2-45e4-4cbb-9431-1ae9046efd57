"""
Сервис для работы с eJabberd через базу данных
"""

from typing import Dict, Any
from datetime import datetime
import xml.etree.ElementTree as ET
from sqlalchemy import text

from core.config import settings
from database.connection import engine


class EjabberdDBService:
    """Сервис для взаимодействия с eJabberd через базу данных"""

    def __init__(self):
        self.domain = settings.ejabberd_domain

    async def get_message_history(
        self, user_jid: str, with_jid: str, limit: int = 50, offset: int = 0
    ) -> Dict[str, Any]:
        """Получение истории сообщений из таблицы archive"""
        try:
            username = user_jid.split("@")[0]
            peer = with_jid

            async with engine.begin() as conn:
                # Получаем сообщения из архива
                query = text("""
                    SELECT timestamp, peer, bare_peer, xml, txt, kind, nick, created_at
                    FROM archive 
                    WHERE username = :username 
                    AND (bare_peer = :peer OR peer = :peer)
                    ORDER BY timestamp DESC 
                    LIMIT :limit OFFSET :offset
                """)

                result = await conn.execute(
                    query,
                    {
                        "username": username,
                        "peer": peer,
                        "limit": limit,
                        "offset": offset,
                    },
                )

                messages = []
                for row in result:
                    # Парсим XML для получения информации о сообщении
                    try:
                        root = ET.fromstring(row.xml)
                        body_elem = root.find(".//{jabber:client}body")
                        body = body_elem.text if body_elem is not None else row.txt

                        # Определяем отправителя
                        from_attr = root.get("from", row.peer)

                        messages.append(
                            {
                                "timestamp": datetime.fromtimestamp(
                                    row.timestamp / 1000000
                                ).isoformat()
                                + "Z",
                                "from": from_attr,
                                "to": root.get("to", user_jid),
                                "body": body,
                                "type": root.get("type", "chat"),
                                "kind": row.kind,
                            }
                        )
                    except ET.ParseError:
                        # Если не удается парсить XML, используем текстовое содержимое
                        messages.append(
                            {
                                "timestamp": datetime.fromtimestamp(
                                    row.timestamp / 1000000
                                ).isoformat()
                                + "Z",
                                "from": row.peer,
                                "to": user_jid,
                                "body": row.txt,
                                "type": "chat",
                                "kind": row.kind,
                            }
                        )

                return {"messages": messages}

        except Exception as e:
            print(f"Error getting message history: {e}")
            return {"messages": []}

    async def send_message_to_archive(
        self, from_jid: str, to_jid: str, body: str, message_type: str = "chat"
    ) -> bool:
        """Сохранение сообщения в архив (имитация отправки)"""
        try:
            from_username = from_jid.split("@")[0]
            to_username = to_jid.split("@")[0]

            # Создаем XML сообщения
            message_xml = f'''<message from="{from_jid}" to="{to_jid}" type="{message_type}" xmlns="jabber:client">
                <body>{body}</body>
                <stanza-id xmlns="urn:xmpp:sid:0" id="msg_{datetime.now().timestamp()}" by="{self.domain}"/>
            </message>'''

            timestamp = int(datetime.now().timestamp() * 1000000)  # микросекунды

            async with engine.begin() as conn:
                # Сохраняем сообщение в архив отправителя
                await conn.execute(
                    text("""
                    INSERT INTO archive (username, timestamp, peer, bare_peer, xml, txt, kind, nick, origin_id)
                    VALUES (:username, :timestamp, :peer, :bare_peer, :xml, :txt, :kind, :nick, :origin_id)
                """),
                    {
                        "username": from_username,
                        "timestamp": timestamp,
                        "peer": to_jid,
                        "bare_peer": to_jid,
                        "xml": message_xml,
                        "txt": body,
                        "kind": "sent",
                        "nick": "",
                        "origin_id": f"msg_{timestamp}",
                    },
                )

                # Сохраняем сообщение в архив получателя
                await conn.execute(
                    text("""
                    INSERT INTO archive (username, timestamp, peer, bare_peer, xml, txt, kind, nick, origin_id)
                    VALUES (:username, :timestamp, :peer, :bare_peer, :xml, :txt, :kind, :nick, :origin_id)
                """),
                    {
                        "username": to_username,
                        "timestamp": timestamp,
                        "peer": from_jid,
                        "bare_peer": from_jid,
                        "xml": message_xml,
                        "txt": body,
                        "kind": "received",
                        "nick": "",
                        "origin_id": f"msg_{timestamp}",
                    },
                )

            return True

        except Exception as e:
            print(f"Error sending message to archive: {e}")
            return False

    async def check_user_exists(self, username: str) -> bool:
        """Проверка существования пользователя в базе данных eJabberd"""
        try:
            async with engine.begin() as conn:
                result = await conn.execute(
                    text("""
                    SELECT COUNT(*) FROM users WHERE username = :username
                """),
                    {"username": username},
                )

                count = result.scalar()
                return count > 0

        except Exception as e:
            print(f"Error checking user exists: {e}")
            return False

    async def get_user_count(self) -> int:
        """Получение количества пользователей"""
        try:
            async with engine.begin() as conn:
                result = await conn.execute(
                    text("""
                    SELECT COUNT(*) FROM users
                """)
                )

                return result.scalar() or 0

        except Exception as e:
            print(f"Error getting user count: {e}")
            return 0
