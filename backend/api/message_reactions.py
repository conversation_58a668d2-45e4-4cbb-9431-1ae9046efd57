"""
API для реакций на сообщения
"""

from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, func
from typing import Dict, List

from database.connection import get_db
from models.message_reaction import MessageReaction
from schemas.message_advanced import (
    MessageReactionCreate,
    MessageReactionResponse,
    MessageReactionSummary,
)
from core.security import get_current_user_jid

router = APIRouter()


@router.post(
    "/add",
    response_model=MessageReactionResponse,
    summary="Добавление реакции на сообщение",
    description="""
    Добавляет эмодзи реакцию на сообщение.

    **Особенности:**
    - Один пользователь может поставить только одну реакцию на сообщение
    - При повторном вызове старая реакция заменяется новой
    - Поддерживаются стандартные Unicode эмодзи
    - Реакция сохраняется в PostgreSQL

    **Поддерживаемые эмодзи:**
    - 👍 👎 ❤️ 😂 😮 😢 😡
    - Любые другие Unicode эмодзи

    **Уведомления:**
    - Автор сообщения получает push уведомление о реакции
    - Реакция отображается в реальном времени
    """,
    responses={
        200: {
            "description": "Реакция успешно добавлена",
            "content": {
                "application/json": {
                    "example": {
                        "id": "123e4567-e89b-12d3-a456-426614174000",
                        "message_id": "msg-123",
                        "user_jid": "john_doe@localhost",
                        "emoji": "👍",
                        "created_at": "2024-01-01T12:00:00Z",
                    }
                }
            },
        },
        401: {"description": "Требуется авторизация"},
        404: {"description": "Сообщение не найдено"},
        422: {"description": "Неверный эмодзи"},
        500: {"description": "Ошибка добавления реакции"},
    },
)
async def add_reaction(
    reaction_data: MessageReactionCreate,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Добавление реакции на сообщение"""
    try:
        # Проверяем, есть ли уже реакция от этого пользователя на это сообщение
        existing_reaction = await db.execute(
            select(MessageReaction).where(
                MessageReaction.message_id == reaction_data.message_id,
                MessageReaction.user_jid == current_user_jid,
            )
        )
        existing = existing_reaction.scalar_one_or_none()

        if existing:
            # Обновляем существующую реакцию
            existing.emoji = reaction_data.emoji
            await db.commit()
            await db.refresh(existing)

            return MessageReactionResponse(
                id=str(existing.id),
                message_id=existing.message_id,
                user_jid=existing.user_jid,
                emoji=existing.emoji,
                created_at=existing.created_at.isoformat()
                if existing.created_at
                else None,
            )
        else:
            # Создаем новую реакцию
            reaction = MessageReaction(
                message_id=reaction_data.message_id,
                user_jid=current_user_jid,
                emoji=reaction_data.emoji,
            )

            db.add(reaction)
            await db.commit()
            await db.refresh(reaction)

            return MessageReactionResponse(
                id=str(reaction.id),
                message_id=reaction.message_id,
                user_jid=reaction.user_jid,
                emoji=reaction.emoji,
                created_at=reaction.created_at.isoformat()
                if reaction.created_at
                else None,
            )

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/remove/{message_id}")
async def remove_reaction(
    message_id: str,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Удаление реакции с сообщения"""
    try:
        result = await db.execute(
            delete(MessageReaction).where(
                MessageReaction.message_id == message_id,
                MessageReaction.user_jid == current_user_jid,
            )
        )

        if result.rowcount == 0:
            raise HTTPException(status_code=404, detail="Reaction not found")

        await db.commit()
        return {"success": True, "message": "Reaction removed"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/message/{message_id}", response_model=MessageReactionSummary)
async def get_message_reactions(
    message_id: str,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Получение всех реакций на сообщение"""
    try:
        result = await db.execute(
            select(MessageReaction)
            .where(MessageReaction.message_id == message_id)
            .order_by(MessageReaction.created_at)
        )
        reactions = result.scalars().all()

        # Группируем реакции по эмодзи
        reactions_dict: Dict[str, List[str]] = {}
        total_count = 0

        for reaction in reactions:
            if reaction.emoji not in reactions_dict:
                reactions_dict[reaction.emoji] = []
            reactions_dict[reaction.emoji].append(reaction.user_jid)
            total_count += 1

        return MessageReactionSummary(
            message_id=message_id, reactions=reactions_dict, total_count=total_count
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/user/{user_jid}")
async def get_user_reactions(
    user_jid: str,
    limit: int = 50,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Получение реакций пользователя"""
    try:
        result = await db.execute(
            select(MessageReaction)
            .where(MessageReaction.user_jid == user_jid)
            .order_by(MessageReaction.created_at.desc())
            .limit(limit)
        )
        reactions = result.scalars().all()

        return [
            MessageReactionResponse(
                id=str(reaction.id),
                message_id=reaction.message_id,
                user_jid=reaction.user_jid,
                emoji=reaction.emoji,
                created_at=reaction.created_at.isoformat()
                if reaction.created_at
                else None,
            )
            for reaction in reactions
        ]

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/popular")
async def get_popular_reactions(
    limit: int = 10,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Получение популярных реакций"""
    try:
        result = await db.execute(
            select(MessageReaction.emoji, func.count(MessageReaction.id).label("count"))
            .group_by(MessageReaction.emoji)
            .order_by(func.count(MessageReaction.id).desc())
            .limit(limit)
        )

        popular_reactions = result.all()

        return [{"emoji": emoji, "count": count} for emoji, count in popular_reactions]

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
