"""
API роутер для аутентификации
"""

from datetime import timedelta
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from database.connection import get_db
from models.user import UserExtended
from schemas.user import UserCreate, UserLogin
from services.ejabberd import EjabberdService
from services.redis import RedisService
from core.security import create_access_token
from core.config import settings

router = APIRouter()

# Инициализируем сервисы
ejabberd_service = EjabberdService()


def get_redis_service() -> RedisService:
    from app import get_redis_service as get_global_redis

    return get_global_redis()


@router.post(
    "/register",
    summary="Регистрация пользователя",
    description="""
    Регистрирует нового пользователя в системе.

    Создает учетную запись в eJabberd XMPP сервере и сохраняет
    дополнительную информацию в PostgreSQL.

    **Что происходит:**
    1. Проверяется уникальность username
    2. Создается пользователь в eJabberd
    3. Сохраняется профиль в нашей БД

    **Требования к паролю:**
    - Минимум 6 символов
    - Рекомендуется использовать буквы, цифры и спецсимволы
    """,
    responses={
        200: {"description": "Пользователь успешно зарегистрирован"},
        400: {"description": "Ошибка валидации или пользователь уже существует"},
        500: {"description": "Внутренняя ошибка сервера"},
    },
)
async def register_user(user_data: UserCreate, db: AsyncSession = Depends(get_db)):
    """Регистрация нового пользователя"""
    try:
        # Создаем пользователя в eJabberd
        success = await ejabberd_service.register_user(
            user_data.username, user_data.password
        )
        if not success:
            raise HTTPException(
                status_code=400, detail="Failed to register user in eJabberd"
            )

        # Сохраняем дополнительную информацию в нашей БД
        user = UserExtended(
            username=user_data.username,
            display_name=user_data.display_name,
            email=user_data.email,
            phone=user_data.phone,
        )

        db.add(user)
        await db.commit()

        return {"success": True, "message": "User registered successfully"}

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=400, detail=str(e))


@router.post(
    "/login",
    summary="Авторизация пользователя",
    description="""
    Авторизует пользователя и возвращает JWT токен.

    **Процесс авторизации:**
    1. Проверяется пароль через eJabberd API
    2. Генерируется JWT токен с JID пользователя
    3. Обновляется статус "онлайн" в Redis

    **Полученный токен используйте в заголовке:**
    ```
    Authorization: Bearer <access_token>
    ```

    **Время жизни токена:** 24 часа (по умолчанию)
    """,
    responses={
        200: {
            "description": "Успешная авторизация",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                        "token_type": "bearer",
                        "user_jid": "john_doe@localhost",
                    }
                }
            },
        },
        401: {"description": "Неверные учетные данные"},
        400: {"description": "Ошибка валидации данных"},
    },
)
async def login_user(user_data: UserLogin):
    """Авторизация пользователя"""
    try:
        # Проверяем пароль через eJabberd
        success = await ejabberd_service.check_password(
            user_data.username, user_data.password
        )

        if not success:
            raise HTTPException(status_code=401, detail="Invalid credentials")

        # Создаем JWT токен
        user_jid = f"{user_data.username}@{settings.ejabberd_domain}"
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": user_jid}, expires_delta=access_token_expires
        )

        # Обновляем статус в Redis
        redis_svc = RedisService()
        await redis_svc.set_user_status(user_jid, "online")

        return {
            "success": True,
            "access_token": access_token,
            "token_type": "bearer",
            "user_jid": user_jid,
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
