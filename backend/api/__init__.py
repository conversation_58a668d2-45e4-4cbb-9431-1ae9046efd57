"""
API роутеры
"""

from fastapi import APIRouter
from .auth import router as auth_router
from .users import router as users_router
from .groups import router as groups_router
from .calls import router as calls_router
from .messages import router as messages_router
from .files import router as files_router
from .notifications import router as notifications_router
from .message_reactions import router as message_reactions_router
from .channels import router as channels_router
from .message_status import router as message_status_router

# Главный роутер API
api_router = APIRouter(prefix="/api")

# Подключаем все роутеры
api_router.include_router(auth_router, prefix="/auth", tags=["Authentication"])
api_router.include_router(users_router, prefix="/users", tags=["Users"])
api_router.include_router(groups_router, prefix="/groups", tags=["Groups"])
api_router.include_router(calls_router, prefix="/calls", tags=["Calls"])
api_router.include_router(messages_router, prefix="/messages", tags=["Messages"])
api_router.include_router(files_router, prefix="/files", tags=["Files"])
api_router.include_router(
    notifications_router, prefix="/notifications", tags=["Notifications"]
)
api_router.include_router(
    message_reactions_router, prefix="/reactions", tags=["Message Reactions"]
)
api_router.include_router(channels_router, prefix="/channels", tags=["Channels"])
api_router.include_router(message_status_router, tags=["Message Status"])


# Health check endpoint
@api_router.get("/health")
async def health_check():
    """Проверка состояния API"""
    return {"status": "healthy", "message": "Messenger API is running"}
