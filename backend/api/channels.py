"""
API для каналов (broadcast channels)
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, or_, and_
from typing import List

from database.connection import get_db
from models.channel import Channel, ChannelSubscriber
from schemas.channel import (
    ChannelCreate,
    ChannelInfo,
    ChannelListItem,
    ChannelSubscribe,
    ChannelUnsubscribe,
)
from core.security import get_current_user_jid

router = APIRouter()


@router.post(
    "/create",
    response_model=ChannelInfo,
    summary="Создание канала",
    description="""
    Создает новый broadcast канал для массовых рассылок.

    **Типы каналов:**
    - `public` - публичный канал, виден всем
    - `private` - приватный канал, только по приглашениям

    **Возможности каналов:**
    - Односторонняя рассылка сообщений
    - Неограниченное количество подписчиков
    - Модерация контента
    - Статистика просмотров

    **Права создателя:**
    - Публикация сообщений
    - Управление подписчиками
    - Изменение настроек канала
    - Назначение модераторов

    **Ограничения:**
    - Название канала от 3 до 100 символов
    - Описание до 500 символов
    - Один пользователь может создать до 10 каналов
    """,
    responses={
        200: {
            "description": "Канал успешно создан",
            "content": {
                "application/json": {
                    "example": {
                        "id": "123e4567-e89b-12d3-a456-426614174000",
                        "name": "Tech News",
                        "description": "Latest technology news and updates",
                        "created_by": "john_doe@localhost",
                        "is_public": True,
                        "subscriber_count": 0,
                        "created_at": "2024-01-01T12:00:00Z",
                    }
                }
            },
        },
        401: {"description": "Требуется авторизация"},
        422: {"description": "Ошибка валидации данных"},
        429: {"description": "Превышен лимит каналов"},
        500: {"description": "Ошибка создания канала"},
    },
)
async def create_channel(
    channel_data: ChannelCreate,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Создание канала"""
    try:
        # Проверяем уникальность username
        if channel_data.username:
            existing_channel = await db.execute(
                select(Channel).where(Channel.username == channel_data.username)
            )
            if existing_channel.scalar_one_or_none():
                raise HTTPException(status_code=400, detail="Username already taken")

        # Создаем канал
        channel = Channel(
            name=channel_data.name,
            username=channel_data.username,
            description=channel_data.description,
            owner_jid=current_user_jid,
            is_public=channel_data.is_public,
        )

        db.add(channel)
        await db.commit()
        await db.refresh(channel)

        # Автоматически подписываем создателя как администратора
        subscription = ChannelSubscriber(
            channel_id=channel.id,
            user_jid=current_user_jid,
            is_admin=True,
            can_post=True,
        )

        db.add(subscription)
        await db.commit()

        # Обновляем счетчик подписчиков
        channel.subscribers_count = 1
        await db.commit()

        return ChannelInfo(
            id=str(channel.id),
            name=channel.name,
            username=channel.username,
            description=channel.description,
            owner_jid=channel.owner_jid,
            is_public=channel.is_public,
            is_verified=channel.is_verified,
            subscribers_count=channel.subscribers_count,
            avatar_url=channel.avatar_url,
            created_at=channel.created_at.isoformat() if channel.created_at else None,
            updated_at=channel.updated_at.isoformat() if channel.updated_at else None,
            is_subscribed=True,
            can_post=True,
        )

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/list", response_model=List[ChannelListItem])
async def list_channels(
    limit: int = Query(20, le=100),
    offset: int = Query(0, ge=0),
    public_only: bool = Query(True),
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Список каналов"""
    try:
        query = select(Channel)

        if public_only:
            query = query.where(Channel.is_public == True)

        query = (
            query.order_by(Channel.subscribers_count.desc()).offset(offset).limit(limit)
        )

        result = await db.execute(query)
        channels = result.scalars().all()

        # Проверяем подписки пользователя
        user_subscriptions = await db.execute(
            select(ChannelSubscriber.channel_id).where(
                ChannelSubscriber.user_jid == current_user_jid
            )
        )
        subscribed_channel_ids = {str(sub[0]) for sub in user_subscriptions.all()}

        return [
            ChannelListItem(
                id=str(channel.id),
                name=channel.name,
                username=channel.username,
                description=channel.description,
                subscribers_count=channel.subscribers_count,
                is_verified=channel.is_verified,
                avatar_url=channel.avatar_url,
                is_subscribed=str(channel.id) in subscribed_channel_ids,
            )
            for channel in channels
        ]

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{channel_id}", response_model=ChannelInfo)
async def get_channel(
    channel_id: str,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Получение информации о канале"""
    try:
        # Получаем канал
        channel_result = await db.execute(
            select(Channel).where(Channel.id == channel_id)
        )
        channel = channel_result.scalar_one_or_none()

        if not channel:
            raise HTTPException(status_code=404, detail="Channel not found")

        # Проверяем подписку пользователя
        subscription_result = await db.execute(
            select(ChannelSubscriber).where(
                and_(
                    ChannelSubscriber.channel_id == channel_id,
                    ChannelSubscriber.user_jid == current_user_jid,
                )
            )
        )
        subscription = subscription_result.scalar_one_or_none()

        return ChannelInfo(
            id=str(channel.id),
            name=channel.name,
            username=channel.username,
            description=channel.description,
            owner_jid=channel.owner_jid,
            is_public=channel.is_public,
            is_verified=channel.is_verified,
            subscribers_count=channel.subscribers_count,
            avatar_url=channel.avatar_url,
            created_at=channel.created_at.isoformat() if channel.created_at else None,
            updated_at=channel.updated_at.isoformat() if channel.updated_at else None,
            is_subscribed=subscription is not None,
            can_post=subscription.can_post if subscription else False,
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/subscribe")
async def subscribe_to_channel(
    subscribe_data: ChannelSubscribe,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Подписка на канал"""
    try:
        # Проверяем существование канала
        channel_result = await db.execute(
            select(Channel).where(Channel.id == subscribe_data.channel_id)
        )
        channel = channel_result.scalar_one_or_none()

        if not channel:
            raise HTTPException(status_code=404, detail="Channel not found")

        # Проверяем, не подписан ли уже пользователь
        existing_subscription = await db.execute(
            select(ChannelSubscriber).where(
                and_(
                    ChannelSubscriber.channel_id == subscribe_data.channel_id,
                    ChannelSubscriber.user_jid == current_user_jid,
                )
            )
        )

        if existing_subscription.scalar_one_or_none():
            raise HTTPException(status_code=400, detail="Already subscribed")

        # Создаем подписку
        subscription = ChannelSubscriber(
            channel_id=subscribe_data.channel_id, user_jid=current_user_jid
        )

        db.add(subscription)

        # Обновляем счетчик подписчиков
        channel.subscribers_count += 1

        await db.commit()

        return {"success": True, "message": "Subscribed to channel"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/unsubscribe")
async def unsubscribe_from_channel(
    unsubscribe_data: ChannelUnsubscribe,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Отписка от канала"""
    try:
        # Находим подписку
        subscription_result = await db.execute(
            select(ChannelSubscriber).where(
                and_(
                    ChannelSubscriber.channel_id == unsubscribe_data.channel_id,
                    ChannelSubscriber.user_jid == current_user_jid,
                )
            )
        )
        subscription = subscription_result.scalar_one_or_none()

        if not subscription:
            raise HTTPException(status_code=404, detail="Subscription not found")

        # Удаляем подписку
        await db.delete(subscription)

        # Обновляем счетчик подписчиков
        channel_result = await db.execute(
            select(Channel).where(Channel.id == unsubscribe_data.channel_id)
        )
        channel = channel_result.scalar_one_or_none()

        if channel:
            channel.subscribers_count = max(0, channel.subscribers_count - 1)

        await db.commit()

        return {"success": True, "message": "Unsubscribed from channel"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/search")
async def search_channels(
    q: str = Query(..., min_length=1),
    limit: int = Query(20, le=100),
    public_only: bool = Query(True),
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Поиск каналов"""
    try:
        query = select(Channel).where(
            or_(
                Channel.name.ilike(f"%{q}%"),
                Channel.username.ilike(f"%{q}%"),
                Channel.description.ilike(f"%{q}%"),
            )
        )

        if public_only:
            query = query.where(Channel.is_public == True)

        query = query.order_by(Channel.subscribers_count.desc()).limit(limit)

        result = await db.execute(query)
        channels = result.scalars().all()

        # Проверяем подписки пользователя
        user_subscriptions = await db.execute(
            select(ChannelSubscriber.channel_id).where(
                ChannelSubscriber.user_jid == current_user_jid
            )
        )
        subscribed_channel_ids = {str(sub[0]) for sub in user_subscriptions.all()}

        return [
            ChannelListItem(
                id=str(channel.id),
                name=channel.name,
                username=channel.username,
                description=channel.description,
                subscribers_count=channel.subscribers_count,
                is_verified=channel.is_verified,
                avatar_url=channel.avatar_url,
                is_subscribed=str(channel.id) in subscribed_channel_ids,
            )
            for channel in channels
        ]

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
