"""
API роутер для пользователей
"""

from typing import List
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from database.connection import get_db
from models.user import UserExtended
from schemas.user import (
    UserProfile,
    UserStatusUpdate,
    UserProfileUpdate,
    UserSearchResult,
)
from services.redis import RedisService
from core.security import get_current_user_jid

router = APIRouter()


# Dependency для получения Redis сервиса
def get_redis_service() -> RedisService:
    from app import get_redis_service as get_global_redis

    return get_global_redis()


@router.get(
    "/profile/{username}",
    response_model=UserProfile,
    summary="Получение профиля пользователя",
    description="""
    Возвращает публичную информацию о пользователе по его username.

    **Возвращаемые данные:**
    - Отображаемое имя
    - Email (если публичный)
    - Телефон (если публичный)
    - Аватар
    - Статус (онлайн/офлайн)
    - Время последней активности
    - Дата регистрации

    **Примечание:** Некоторые поля могут быть скрыты в зависимости от настроек приватности пользователя.
    """,
    responses={
        200: {
            "description": "Профиль пользователя",
            "content": {
                "application/json": {
                    "example": {
                        "username": "john_doe",
                        "display_name": "John Doe",
                        "email": "<EMAIL>",
                        "phone": "+1234567890",
                        "avatar_url": "https://example.com/avatar.jpg",
                        "status": "online",
                        "last_seen": "2024-01-01T12:00:00Z",
                        "created_at": "2024-01-01T10:00:00Z",
                    }
                }
            },
        },
        404: {"description": "Пользователь не найден"},
        401: {"description": "Требуется авторизация"},
    },
)
async def get_user_profile(
    username: str,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Получение профиля пользователя"""
    result = await db.execute(
        select(UserExtended).where(UserExtended.username == username)
    )
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    return UserProfile.model_validate(user)


@router.post(
    "/status",
    summary="Обновление статуса пользователя",
    description="""
    Обновляет статус присутствия пользователя.

    **Поддерживаемые статусы:**
    - `online` - пользователь онлайн
    - `away` - отошел
    - `busy` - занят
    - `offline` - офлайн

    **Что происходит:**
    1. Статус обновляется в Redis для быстрого доступа
    2. Статус сохраняется в PostgreSQL
    3. Другие пользователи видят обновленный статус

    **Примечание:** Статус автоматически меняется на `offline` при отключении от XMPP.
    """,
    responses={
        200: {
            "description": "Статус успешно обновлен",
            "content": {
                "application/json": {"example": {"success": True, "status": "online"}}
            },
        },
        401: {"description": "Требуется авторизация"},
        422: {"description": "Неверный статус"},
        500: {"description": "Ошибка обновления статуса"},
    },
)
async def update_user_status(
    status_data: UserStatusUpdate,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Обновление статуса пользователя"""
    try:
        # Обновляем в Redis
        redis_svc = RedisService()
        await redis_svc.set_user_status(current_user_jid, status_data.status)

        # Обновляем в БД
        username = current_user_jid.split("@")[0]
        result = await db.execute(
            select(UserExtended).where(UserExtended.username == username)
        )
        user = result.scalar_one_or_none()

        if user:
            user.status = status_data.status
            await db.commit()

        return {"success": True, "status": status_data.status}

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/profile/update",
    summary="Обновление профиля пользователя",
    description="""
    Обновляет информацию в профиле текущего пользователя.

    **Обновляемые поля:**
    - `display_name` - отображаемое имя
    - `email` - адрес электронной почты
    - `phone` - номер телефона

    **Особенности:**
    - Обновляются только переданные поля (частичное обновление)
    - Поля со значением `null` игнорируются
    - Валидация email и телефона
    - Изменения сохраняются в PostgreSQL

    **Примечание:** Username изменить нельзя, так как он используется в XMPP JID.
    """,
    responses={
        200: {
            "description": "Профиль успешно обновлен",
            "content": {
                "application/json": {
                    "example": {"success": True, "message": "Profile updated"}
                }
            },
        },
        401: {"description": "Требуется авторизация"},
        404: {"description": "Пользователь не найден"},
        422: {"description": "Ошибка валидации данных"},
        500: {"description": "Ошибка обновления профиля"},
    },
)
async def update_profile(
    profile_data: UserProfileUpdate,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Обновление профиля пользователя"""
    try:
        username = current_user_jid.split("@")[0]
        result = await db.execute(
            select(UserExtended).where(UserExtended.username == username)
        )
        user = result.scalar_one_or_none()

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Обновляем только переданные поля
        if profile_data.display_name is not None:
            user.display_name = profile_data.display_name
        if profile_data.email is not None:
            user.email = profile_data.email
        if profile_data.phone is not None:
            user.phone = profile_data.phone

        await db.commit()
        return {"success": True, "message": "Profile updated"}

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/search",
    response_model=List[UserSearchResult],
    summary="Поиск пользователей",
    description="""
    Поиск пользователей по имени пользователя или отображаемому имени.

    **Параметры поиска:**
    - `q` - поисковый запрос (минимум 2 символа)
    - `limit` - максимальное количество результатов (по умолчанию 10, максимум 50)

    **Алгоритм поиска:**
    - Поиск по username (регистронезависимый)
    - Поиск по display_name (регистронезависимый)
    - Частичное совпадение (LIKE %query%)
    - Сортировка по релевантности

    **Возвращаемые данные:**
    - Только публичная информация
    - Username, display_name, avatar_url
    - Без приватных данных (email, phone)
    """,
    responses={
        200: {
            "description": "Результаты поиска",
            "content": {
                "application/json": {
                    "example": [
                        {
                            "username": "john_doe",
                            "display_name": "John Doe",
                            "avatar_url": "https://example.com/avatar.jpg",
                        },
                        {
                            "username": "jane_doe",
                            "display_name": "Jane Doe",
                            "avatar_url": None,
                        },
                    ]
                }
            },
        },
        401: {"description": "Требуется авторизация"},
        422: {"description": "Слишком короткий поисковый запрос"},
    },
)
async def search_users(
    q: str,
    limit: int = 10,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Поиск пользователей"""
    result = await db.execute(
        select(UserExtended)
        .where(
            (UserExtended.username.ilike(f"%{q}%"))
            | (UserExtended.display_name.ilike(f"%{q}%"))
        )
        .limit(limit)
    )
    users = result.scalars().all()

    return [UserSearchResult.model_validate(user) for user in users]


@router.get(
    "/online",
    summary="Список онлайн пользователей",
    description="""
    Возвращает список пользователей, которые сейчас онлайн.

    **Источник данных:**
    - Информация берется из Redis кэша
    - Обновляется в реальном времени при подключении/отключении XMPP
    - Статусы: online, away, busy

    **Возвращаемые данные:**
    - JID пользователя
    - Username (извлекается из JID)
    - Текущий статус

    **Применение:**
    - Отображение списка активных пользователей
    - Проверка доступности для звонков
    - Индикаторы присутствия в UI
    """,
    responses={
        200: {
            "description": "Список онлайн пользователей",
            "content": {
                "application/json": {
                    "example": {
                        "online_users": [
                            {
                                "user_jid": "john_doe@localhost",
                                "username": "john_doe",
                                "status": "online",
                            },
                            {
                                "user_jid": "jane_doe@localhost",
                                "username": "jane_doe",
                                "status": "away",
                            },
                        ]
                    }
                }
            },
        },
        401: {"description": "Требуется авторизация"},
        500: {"description": "Ошибка получения данных из Redis"},
    },
)
async def get_online_users(current_user_jid: str = Depends(get_current_user_jid)):
    """Получение списка онлайн пользователей"""
    try:
        redis_svc = RedisService()
        online_users = await redis_svc.get_online_users()

        result = []
        for user_jid, status in online_users.items():
            username = user_jid.split("@")[0]
            result.append(
                {"user_jid": user_jid, "username": username, "status": status}
            )

        return {"online_users": result}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
