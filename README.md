# 🚀 Messenger Platform

> *Когда WhatsApp встречает Telegram, а разработчики не хотят изобретать велосипед* 🛠️

Полнофункциональная платформа мессенджера, которая объединяет лучшие open-source решения в единую экосистему. Никаких костылей, только проверенные временем технологии!

## 🎯 Философия проекта

**"Зачем писать XMPP сервер с нуля, если есть eJabberd?"** 🔥
**"Зачем изобретать WebRTC, если есть LiveKit?"** 📹
**"Зачем мучиться с файлами, если есть S3?"** 📦

Мы взяли лучшее из каждой области и склеили это элегантным FastAPI бекендом. Результат? Мессенджер enterprise-уровня за выходные! 🎉

## 🏗️ Архитектура

```
    📱 Mobile     💻 Web      🖥️ Desktop
         │          │           │
         └──────────┼───────────┘
                    │
         ┌──────────▼──────────┐
         │   🚀 FastAPI        │  ← Наш умный прокси
         │     Backend         │
         └──────────┬──────────┘
                    │
    ┌───────────────┼───────────────┐
    │               │               │
┌───▼───┐    ┌──────▼──────┐    ┌───▼───┐
│ 🔥     │    │ 📹 LiveKit  │    │ 📦    │
│eJabberd│    │ (Звонки)    │    │MinIO  │
│(XMPP)  │    │             │    │ (S3)  │
└────────┘    └─────────────┘    └───────┘
    │               │               │
    └───────────────┼───────────────┘
                    │
         ┌──────────▼──────────┐
         │   🐘 PostgreSQL     │
         │   ⚡ Redis          │
         └─────────────────────┘
```

### 🧩 Компоненты

- **🔥 eJabberd** - Боевой XMPP сервер для сообщений и групповых чатов
- **📹 LiveKit** - Медиа сервер для кристально чистых звонков
- **🚀 FastAPI** - Наш REST API прокси со сверхспособностями
- **🐘 PostgreSQL** - Надежная база для метаданных
- **⚡ Redis** - Молниеносное кэширование и сессии
- **📦 MinIO** - S3-совместимое хранилище файлов

## ✨ Возможности

### ✅ **Работает прямо сейчас**
- 🔐 Регистрация и авторизация (JWT токены)
- 💬 Отправка сообщений через нативный XMPP
- 📁 Загрузка файлов в S3 хранилище
- 👥 Групповые чаты на MUC технологии
- 📞 Аудио/видео звонки через LiveKit
- 📚 История сообщений (MAM)
- 👤 Управление пользователями и поиск
- 📊 Статусы онлайн/офлайн в реальном времени
- 😊 Реакции эмодзи на сообщения
- 📺 Broadcast каналы для массовых рассылок
- 🔔 Push уведомления (готово к интеграции)

### 🔄 **В активной разработке**
- 🔒 End-to-end шифрование сообщений
- 🛡️ Модерация контента и антиспам
- 📈 Аналитика и детальные метрики

## 🚀 Быстрый старт

### 📋 Предварительные требования
- Docker и Docker Compose
- Python 3.12+ с uv (для разработки)
- 4GB RAM минимум (рекомендуется 8GB)

### ⚡ Запуск за 3 команды

1. **Клонируйте и перейдите в папку:**
```bash
git clone <repository-url>
cd ejabberd_fastapi1
```

2. **Запустите всю инфраструктуру:**
```bash
docker compose up -d
```

3. **Дождитесь магии (1-2 минуты) и проверьте:**
```bash
docker compose ps
curl http://localhost:8000/api/health
```

### 🎉 Проверка работоспособности

Откройте в браузере:
- 📖 **Swagger UI**: http://localhost:8000/docs
- 🔍 **ReDoc**: http://localhost:8000/redoc
- 🔧 **eJabberd Admin**: http://localhost:5280/admin (admin/admin123)
- 📦 **MinIO Console**: http://localhost:9001 (minioadmin/minioadmin123)

### 🧪 Тестирование API

```bash
# Быстрый тест
cd backend
python test_api.py

# Или полный набор тестов
python test_modern_features.py
```

### 🎯 Первые шаги

```bash
# 1. Регистрируем пользователя
curl -X POST http://localhost:8000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username": "alice", "password": "secret123", "display_name": "Alice"}'

# 2. Авторизуемся
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "alice", "password": "secret123"}'

# 3. Отправляем сообщение (используйте токен из п.2)
curl -X POST http://localhost:8000/api/messages/send \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{"to_jid": "admin@localhost", "body": "Привет из API! 👋"}'
```

## 📚 API Endpoints

> **Полная документация**: [API Reference](backend/docs/api_reference.md)

### 🔐 **Аутентификация**
- `POST /api/auth/register` - Регистрация нового пользователя
- `POST /api/auth/login` - Авторизация и получение JWT токена

### 👤 **Пользователи**
- `GET /api/users/profile/{username}` - Профиль пользователя
- `GET /api/users/search?q={query}` - Поиск по имени
- `GET /api/users/online` - Кто сейчас онлайн
- `POST /api/users/status` - Обновить свой статус
- `POST /api/users/profile/update` - Редактировать профиль

### 💬 **Сообщения**
- `POST /api/messages/send` - Отправить сообщение
- `GET /api/messages/history/{jid}` - История переписки
- `POST /api/messages/reply` - Ответить на сообщение
- `POST /api/messages/forward` - Переслать сообщение
- `PUT /api/messages/edit` - Редактировать сообщение
- `DELETE /api/messages/delete` - Удалить сообщение

### 👥 **Групповые чаты**
- `POST /api/groups/create` - Создать группу
- `GET /api/groups/list` - Мои группы
- `GET /api/groups/{group_id}/info` - Информация о группе
- `GET /api/groups/{group_id}/members` - Участники
- `POST /api/groups/{group_id}/invite` - Пригласить в группу
- `DELETE /api/groups/{group_id}/members/{user_jid}` - Исключить

### 📁 **Файлы**
- `POST /api/files/upload` - Загрузить файл
- `GET /api/files/{file_id}` - Скачать файл

### 📞 **Звонки**
- `POST /api/calls/create` - Начать звонок
- `GET /api/calls/{call_id}` - Статус звонка
- `POST /api/calls/{call_id}/end` - Завершить звонок

### 🔔 **Уведомления**
- `GET /api/notifications` - Список уведомлений
- `POST /api/notifications/mark-read` - Отметить как прочитанные
- `GET /api/notifications/unread-count` - Счетчик непрочитанных

### 😊 **Реакции**
- `POST /api/reactions/add` - Поставить реакцию
- `DELETE /api/reactions/remove/{message_id}` - Убрать реакцию
- `GET /api/reactions/message/{message_id}` - Все реакции на сообщение

### 📺 **Каналы**
- `POST /api/channels/create` - Создать канал
- `GET /api/channels/list` - Список каналов
- `POST /api/channels/{channel_id}/subscribe` - Подписаться
- `POST /api/channels/{channel_id}/post` - Опубликовать пост

## ⚙️ Конфигурация

### 🔧 Переменные окружения

Все настройки уже прописаны в `docker-compose.yml`, но для production обязательно измените:

```yaml
# 🔐 Безопасность (ОБЯЗАТЕЛЬНО ПОМЕНЯТЬ!)
EJABBERD_ADMIN_PASSWORD: admin123          # ← Смените!
MINIO_ROOT_PASSWORD: minioadmin123         # ← Смените!
SECRET_KEY: your-super-secret-key          # ← Смените!

# 🗄️ База данных
DATABASE_URL: ***********************************************/messenger
REDIS_URL: redis://redis:6379

# 📦 Файловое хранилище
MINIO_ENDPOINT: minio:9000
MINIO_ACCESS_KEY: minioadmin
MINIO_SECRET_KEY: minioadmin123

# 🔥 eJabberd XMPP
EJABBERD_API_URL: http://ejabberd:5280/api
EJABBERD_ADMIN_USER: admin
EJABBERD_DOMAIN: localhost

# 📹 LiveKit звонки
LIVEKIT_API_KEY: APIKey
LIVEKIT_API_SECRET: secret123
LIVEKIT_URL: ws://livekit:7880
```

### 🌐 Порты сервисов

- **8000** - 🚀 FastAPI backend (главный API)
- **5222** - 🔥 XMPP клиентские соединения
- **5280** - 🔧 eJabberd HTTP API и админка
- **7880** - 📹 LiveKit RTC сервер
- **5432** - 🐘 PostgreSQL база данных
- **6379** - ⚡ Redis кэш
- **9000/9001** - 📦 MinIO S3 хранилище

### 🚀 Production чеклист

#### ⚠️ **Критически важно:**
- [ ] 🔐 Смените ВСЕ пароли по умолчанию
- [ ] 🔒 Настройте HTTPS для всех сервисов
- [ ] 🛡️ Настройте firewall и ограничьте доступ
- [ ] 📜 Получите SSL сертификаты
- [ ] 🔍 Настройте мониторинг и алерты
- [ ] 💾 Автоматические бэкапы PostgreSQL

## 🛠️ Разработка

### 💻 Локальная разработка

1. **Запустите инфраструктуру:**
```bash
docker compose up -d postgres redis minio ejabberd livekit
```

2. **Установите зависимости:**
```bash
cd backend
uv sync
```

3. **Запустите FastAPI в режиме разработки:**
```bash
uv run uvicorn app:app --reload --host 0.0.0.0 --port 8000
```

4. **Откройте Swagger UI:** http://localhost:8000/docs

### 📁 Структура проекта

```
📦 ejabberd_fastapi1/
├── 🚀 backend/                 # FastAPI бекенд
│   ├── 🌐 api/                # REST API роутеры
│   ├── ⚙️ core/               # Конфигурация и безопасность
│   ├── 🗄️ database/           # Подключение к БД
│   ├── 📊 models/             # SQLAlchemy модели
│   ├── 📝 schemas/            # Pydantic схемы
│   ├── 🔧 services/           # Бизнес-логика
│   ├── 🔄 alembic/            # Миграции БД
│   ├── 📚 docs/               # Документация бекенда
│   ├── app.py                 # Главный файл FastAPI
│   ├── pyproject.toml         # Зависимости (uv)
│   └── Dockerfile             # Docker образ
├── 🔥 ejabberd/               # Конфигурация XMPP сервера
│   ├── ejabberd.yml          # Основные настройки
│   └── database.yml          # Подключение к БД
├── 📹 livekit/                # Конфигурация медиа сервера
│   └── livekit.yaml          # Настройки звонков
├── 🗄️ sql/                    # SQL скрипты
│   └── init.sql              # Инициализация БД
├── 📚 docs/                  # Документация проекта
├── docker-compose.yml         # Оркестрация всех сервисов
└── README.md                  # Этот файл
```

### 📚 Документация

- 🏗️ **[Project Structure](docs/project_structure.md)** - Архитектура и организация кода
- 📋 **[API Reference](backend/docs/api_reference.md)** - Полное описание всех эндпойнтов
- 🗄️ **[Database Schema](backend/docs/database_schema.md)** - Схема БД и миграции
- 🚀 **[Backend README](backend/README.md)** - Подробности о FastAPI бекенде

## 📱 XMPP клиенты

Наш бекенд полностью совместим с любыми XMPP клиентами!

### 🔌 Настройки подключения:
- **Сервер:** `localhost:5222`
- **Домен:** `localhost`
- **Пользователи:** Создаются через REST API или админку eJabberd

### 📲 Рекомендуемые клиенты:
- **🖥️ Desktop:** Gajim, Psi+, Swift.im
- **📱 Android:** Conversations, Blabber.im
- **🍎 iOS:** ChatSecure, Monal
- **🌐 Web:** Converse.js, JSXC

## 📊 Мониторинг и отладка

### 📋 Логи
```bash
# Все сервисы сразу
docker compose logs -f

# Конкретные сервисы
docker compose logs -f fastapi_backend
docker compose logs -f ejabberd
docker compose logs -f livekit

# Последние 100 строк
docker compose logs --tail=100 fastapi_backend
```

### 📈 Метрики и админки
- 🔧 **eJabberd Admin:** http://localhost:5280/admin (admin/admin123)
- 📦 **MinIO Console:** http://localhost:9001 (minioadmin/minioadmin123)
- 🚀 **API Health:** http://localhost:8000/api/health
- 📖 **API Docs:** http://localhost:8000/docs

### 🔍 Полезные команды
```bash
# Статус всех контейнеров
docker compose ps

# Перезапуск конкретного сервиса
docker compose restart fastapi_backend

# Подключение к PostgreSQL
docker compose exec postgres psql -U postgres -d messenger

# Подключение к Redis
docker compose exec redis redis-cli

# Просмотр использования ресурсов
docker stats
```

## 🤝 Сообщество и поддержка

### 🐛 Нашли баг?
1. Проверьте [Issues](https://github.com/your-repo/issues)
2. Создайте новый issue с подробным описанием
3. Приложите логи и шаги воспроизведения

### 💡 Есть идея?
- Создайте [Discussion](https://github.com/your-repo/discussions)
- Опишите use case и ожидаемое поведение
- Мы обсудим реализацию!

### 🤝 Хотите помочь?
1. Fork репозитория
2. Создайте feature branch
3. Сделайте изменения с тестами
4. Создайте Pull Request

## 📄 Лицензия

**MIT License** - используйте как хотите, но не забудьте поставить ⭐!

---

<div align="center">

**Сделано с ❤️ для разработчиков, которые ценят простоту и надежность**

*Если проект помог - поставьте звездочку! Это мотивирует на дальнейшее развитие* ⭐

[🐛 Сообщить о баге](https://github.com/your-repo/issues) • [💡 Предложить идею](https://github.com/your-repo/discussions) • [📚 Документация](backend/docs/) • [💬 Telegram](https://t.me/messenger_support)

</div>
